import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// 创建一个全局共享的axios实例，这样mock adapter可以拦截所有请求
const sharedAxiosInstance = axios.create({
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  }
});

// 导出共享实例供mock adapter使用
export { sharedAxiosInstance };

/**
 * 基础API服务类
 * 封装HTTP请求方法和错误处理
 */
export class BaseApiService {
  protected api: AxiosInstance;
  protected baseURL: string;

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL;
    // 使用共享的axios实例，这样mock adapter可以拦截所有请求
    this.api = sharedAxiosInstance;
    
    // 确保每个请求都带有正确的baseURL
    this.api.defaults.baseURL = baseURL;

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors(): void {
    // 请求拦截器
    this.api.interceptors.request.use(
      (config) => {
        // 可以在这里添加认证令牌等
        // 例如: config.headers.Authorization = `Bearer ${getToken()}`;
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.api.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        // 统一错误处理
        this.handleApiError(error);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 处理API错误
   */
  protected handleApiError(error: any): void {
    if (error.response) {
      // 服务器返回错误响应
      const status = error.response.status;
      const data = error.response.data;
      
      console.error(`API Error (${status}):`, data);
      
      // 可以根据状态码进行不同处理
      switch (status) {
        case 401:
          // 未授权，可以重定向到登录页面
          console.error('Authentication error');
          break;
        case 403:
          // 权限不足
          console.error('Permission denied');
          break;
        case 404:
          // 资源不存在
          console.error('Resource not found');
          break;
        case 500:
          // 服务器错误
          console.error('Server error');
          break;
        default:
          console.error('API request failed');
      }
    } else if (error.request) {
      // 请求已发送但没有收到响应
      console.error('No response received:', error.request);
    } else {
      // 请求配置出错
      console.error('Request configuration error:', error.message);
    }
  }

  /**
   * GET请求
   */
  protected async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.get(url, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * POST请求
   */
  protected async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.post(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PUT请求
   */
  protected async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.put(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * DELETE请求
   */
  protected async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.delete(url, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  /**
   * PATCH请求
   */
  protected async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.patch(url, data, config);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}

/**
 * API响应接口
 */
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}
