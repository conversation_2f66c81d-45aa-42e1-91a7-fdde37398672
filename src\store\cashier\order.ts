import { defineStore } from "pinia";
import { ref, computed } from "vue";
import type { Order, OrderForm, Product } from "@/views/cashier/types/product";

export const useOrderStore = defineStore("cashier-order", () => {
  // 状态
  const orders = ref<Order[]>([]);
  const products = ref<Product[]>([]);

  // 计算属性
  const getOrdersByRoom = computed(() => {
    return (roomId: string) =>
      orders.value.filter(order => order.roomId === roomId);
  });

  const getPendingOrders = computed(() => {
    return orders.value.filter(order => order.status === "pending");
  });

  const getTotalOrderAmount = computed(() => {
    return (roomId: string) => {
      const roomOrders = orders.value.filter(order => order.roomId === roomId);
      return roomOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    };
  });

  // 方法
  const createOrder = (orderForm: OrderForm): Order => {
    const orderId = `ORDER_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;

    const order: Order = {
      id: orderId,
      roomId: orderForm.roomId,
      roomType: getRoomTypeById(orderForm.roomId),
      items: orderForm.items,
      totalAmount: orderForm.items.reduce(
        (sum, item) => sum + item.subtotal,
        0
      ),
      totalQuantity: orderForm.items.reduce(
        (sum, item) => sum + item.quantity,
        0
      ),
      status: "pending",
      notes: orderForm.notes,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: "current_cashier" // TODO: 从用户状态获取
    };

    orders.value.push(order);

    // 保存到本地存储
    saveToLocalStorage();

    return order;
  };

  const updateOrderStatus = (orderId: string, status: Order["status"]) => {
    const order = orders.value.find(o => o.id === orderId);
    if (order) {
      order.status = status;
      order.updatedAt = new Date().toISOString();

      if (status === "served") {
        order.servedAt = new Date().toISOString();
      }

      saveToLocalStorage();
    }
  };

  const cancelOrder = (orderId: string) => {
    updateOrderStatus(orderId, "cancelled");
  };

  const deleteOrder = (orderId: string) => {
    const index = orders.value.findIndex(o => o.id === orderId);
    if (index > -1) {
      orders.value.splice(index, 1);
      saveToLocalStorage();
    }
  };

  const getRoomTypeById = (roomId: string): string => {
    // 根据房间ID判断房间类型
    if (roomId.startsWith("V")) return "VIP包厢";
    if (roomId.startsWith("M")) return "中包厢";
    if (roomId.startsWith("S")) return "小包厢";
    return "普通包厢";
  };

  // 本地存储
  const saveToLocalStorage = () => {
    try {
      localStorage.setItem("cashier_orders", JSON.stringify(orders.value));
    } catch (error) {
      console.error("保存订单到本地存储失败:", error);
    }
  };

  const loadFromLocalStorage = () => {
    try {
      const stored = localStorage.getItem("cashier_orders");
      if (stored) {
        orders.value = JSON.parse(stored);
      }
    } catch (error) {
      console.error("从本地存储加载订单失败:", error);
    }
  };

  // 初始化模拟商品数据
  const initializeProducts = () => {
    products.value = [
      {
        id: "1",
        name: "可口可乐",
        price: 8,
        category: "beverage",
        description: "经典可乐",
        stock: 50,
        unit: "瓶",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      {
        id: "2",
        name: "薯片",
        price: 15,
        category: "snack",
        description: "香脆薯片",
        stock: 30,
        unit: "包",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      {
        id: "3",
        name: "苹果拼盘",
        price: 25,
        category: "fruit",
        description: "新鲜苹果拼盘",
        stock: 20,
        unit: "份",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      {
        id: "4",
        name: "青岛啤酒",
        price: 12,
        category: "alcohol",
        description: "经典啤酒",
        stock: 40,
        unit: "瓶",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      {
        id: "5",
        name: "中华香烟",
        price: 25,
        category: "cigarette",
        description: "软包中华",
        stock: 15,
        unit: "包",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      {
        id: "6",
        name: "雪碧",
        price: 8,
        category: "beverage",
        description: "柠檬味汽水",
        stock: 45,
        unit: "瓶",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      {
        id: "7",
        name: "花生米",
        price: 12,
        category: "snack",
        description: "香酥花生米",
        stock: 25,
        unit: "盘",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      {
        id: "8",
        name: "西瓜拼盘",
        price: 30,
        category: "fruit",
        description: "新鲜西瓜拼盘",
        stock: 15,
        unit: "份",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      {
        id: "9",
        name: "红酒",
        price: 88,
        category: "alcohol",
        description: "进口红酒",
        stock: 10,
        unit: "瓶",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      },
      {
        id: "10",
        name: "纸巾",
        price: 5,
        category: "other",
        description: "餐巾纸",
        stock: 100,
        unit: "包",
        isActive: true,
        createdAt: "2025-01-01",
        updatedAt: "2025-01-01"
      }
    ];
  };

  // 清空所有数据（用于测试）
  const clearAllData = () => {
    orders.value = [];
    localStorage.removeItem("cashier_orders");
  };

  return {
    // 状态
    orders,
    products,

    // 计算属性
    getOrdersByRoom,
    getPendingOrders,
    getTotalOrderAmount,

    // 方法
    createOrder,
    updateOrderStatus,
    cancelOrder,
    deleteOrder,
    loadFromLocalStorage,
    initializeProducts,
    clearAllData
  };
});

export const useCashierOrder = () => useOrderStore();
