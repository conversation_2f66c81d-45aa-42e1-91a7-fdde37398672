<template>
  <!-- 结账确认弹窗 -->
  <div
    class="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center"
  >
    <div
      class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden border border-gray-200"
    >
      <!-- 弹窗头部 -->
      <div class="bg-gradient-to-r from-green-600 to-blue-600 text-white p-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
              <font-awesome-icon icon="receipt" class="text-white text-lg" />
            </div>
            <div>
              <h2 class="text-2xl font-bold">结账确认</h2>
              <p class="text-blue-100">{{ roomInfo.id }} - {{ roomInfo.type }}</p>
            </div>
          </div>
          <button
            @click="$emit('close')"
            class="w-10 h-10 flex items-center justify-center text-white/70 hover:text-white hover:bg-white/20 rounded-xl transition-all duration-200"
          >
            <font-awesome-icon icon="times" class="text-lg" />
          </button>
        </div>
      </div>

      <!-- 弹窗内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
        <div class="space-y-6">
          <!-- 房间信息卡片 -->
          <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-100">
            <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
              <font-awesome-icon icon="door-open" class="text-blue-600 mr-2" />
              房间信息
            </h3>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">房间号：</span>
                <span class="font-medium">{{ roomInfo.id }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">房型：</span>
                <span class="font-medium">{{ roomInfo.type }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">开房时间：</span>
                <span class="font-medium">{{ roomInfo.startTime || currentTime }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">使用时长：</span>
                <span class="font-medium">{{ roomInfo.duration || '2小时30分' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">客户：</span>
                <span class="font-medium">{{ roomInfo.customer || '散客' }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">收银员：</span>
                <span class="font-medium">当前收银员</span>
              </div>
            </div>
          </div>

          <!-- 订单明细 -->
          <div class="bg-white rounded-xl border border-gray-200">
            <div class="p-4 border-b border-gray-100">
              <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <font-awesome-icon icon="list-ul" class="text-green-600 mr-2" />
                消费明细
              </h3>
            </div>
            <div class="p-4">
              <!-- 房费 -->
              <div class="flex items-center justify-between py-3 border-b border-gray-50">
                <div class="flex-1">
                  <div class="flex items-center justify-between">
                    <span class="font-medium text-gray-800">房费</span>
                    <span class="text-sm text-gray-500">{{ roomInfo.type }}</span>
                  </div>
                  <div class="text-sm text-gray-600 mt-1">
                    使用时长：{{ roomInfo.duration || '2小时30分' }}
                  </div>
                </div>
                <div class="text-right ml-4">
                  <div class="font-semibold text-gray-800">¥{{ roomFee }}</div>
                </div>
              </div>

              <!-- 商品订单 -->
              <div v-if="roomOrders.length > 0" class="space-y-3 mt-3">
                <div
                  v-for="order in roomOrders"
                  :key="order.id"
                  class="border border-gray-100 rounded-lg p-3"
                >
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">
                      订单 #{{ order.id.slice(-6) }}
                    </span>
                    <span class="text-xs text-gray-500">
                      {{ formatOrderTime(order.createdAt) }}
                    </span>
                  </div>
                  <div class="space-y-2">
                    <div
                      v-for="item in order.items"
                      :key="item.productId"
                      class="flex items-center justify-between py-1"
                    >
                      <div class="flex-1">
                        <div class="flex items-center justify-between">
                          <span class="font-medium text-gray-800">{{ item.product.name }}</span>
                          <span class="text-sm text-gray-500">{{ item.product.unit }}</span>
                        </div>
                        <div class="text-sm text-gray-600">
                          ¥{{ item.product.price }} × {{ item.quantity }}
                        </div>
                      </div>
                      <div class="text-right ml-4">
                        <div class="font-semibold text-gray-800">¥{{ item.subtotal }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 无商品消费提示 -->
              <div v-else class="text-center py-6 text-gray-500">
                <font-awesome-icon icon="shopping-cart" class="text-2xl mb-2" />
                <p>暂无商品消费</p>
              </div>
            </div>
          </div>

          <!-- 费用汇总 -->
          <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-4 border border-orange-100">
            <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
              <font-awesome-icon icon="calculator" class="text-orange-600 mr-2" />
              费用汇总
            </h3>
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600">房费：</span>
                <span class="font-medium">¥{{ roomFee }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">商品消费：</span>
                <span class="font-medium">¥{{ totalOrderAmount }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">服务费：</span>
                <span class="font-medium">¥{{ serviceFee }}</span>
              </div>
              <div class="flex justify-between border-t border-orange-200 pt-2 mt-2">
                <span class="text-gray-600">折扣：</span>
                <span class="font-medium text-green-600">-¥{{ discount }}</span>
              </div>
              <div class="flex justify-between border-t border-orange-200 pt-2 mt-2 text-lg">
                <span class="font-semibold text-gray-800">应收总计：</span>
                <span class="font-bold text-red-600">¥{{ finalTotal }}</span>
              </div>
            </div>
          </div>

          <!-- 备注信息 -->
          <div class="bg-gray-50 rounded-xl p-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              结账备注（可选）
            </label>
            <textarea
              v-model="checkoutNotes"
              placeholder="请输入结账备注信息..."
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮 -->
      <div class="p-6 border-t border-gray-200 bg-gray-50">
        <div class="flex space-x-4">
          <button
            @click="$emit('close')"
            class="flex-1 py-3 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors font-medium"
          >
            <font-awesome-icon icon="arrow-left" class="mr-2" />
            取消
          </button>
          <button
            @click="confirmCheckout"
            class="flex-1 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-200 font-semibold"
          >
            <font-awesome-icon icon="check-circle" class="mr-2" />
            确认结账
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type { Room } from '../../types/room';
import type { Order } from '../../types/product';

defineOptions({
  name: 'CheckoutConfirmationModal'
});

// Props
const props = defineProps<{
  roomInfo: Room;
  roomOrders: Order[];
}>();

// Emits
const emit = defineEmits<{
  close: [];
  confirm: [checkoutData: {
    roomId: string;
    totalAmount: number;
    notes?: string;
    orders: Order[];
  }];
}>();

// 响应式数据
const checkoutNotes = ref('');

// 费用配置
const roomFee = ref(50); // 房费，实际应该根据房型和时长计算
const serviceFee = ref(10); // 服务费
const discount = ref(0); // 折扣

// 计算属性
const totalOrderAmount = computed(() => {
  return props.roomOrders.reduce((sum, order) => sum + order.totalAmount, 0);
});

const finalTotal = computed(() => {
  return roomFee.value + totalOrderAmount.value + serviceFee.value - discount.value;
});

const currentTime = computed(() => {
  return new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
});

// 方法
const formatOrderTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const confirmCheckout = () => {
  const checkoutData = {
    roomId: props.roomInfo.id,
    totalAmount: finalTotal.value,
    notes: checkoutNotes.value || undefined,
    orders: props.roomOrders
  };

  emit('confirm', checkoutData);
  ElMessage.success(`房间 ${props.roomInfo.id} 结账确认完成！`);
};
</script>

<style scoped>
/* 结账确认弹窗样式 */
.checkout-modal {
  font-family: "Inter", "Noto Sans SC", sans-serif;
}
</style>
