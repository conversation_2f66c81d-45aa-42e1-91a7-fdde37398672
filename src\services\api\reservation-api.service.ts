import { BaseApiService, ApiResponse } from './base-api';

/**
 * 预订状态
 */
export type ReservationStatus = 'pending' | 'confirmed' | 'checked-in' | 'cancelled' | 'completed';

/**
 * 预订信息接口
 */
export interface Reservation {
  id: string;
  roomId: string;
  customerId?: string;
  customerName: string;
  customerPhone: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  status: ReservationStatus;
  notes?: string;
  deposit?: number;
  estimatedPeople: number;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
}

/**
 * 预订查询参数
 */
export interface ReservationQuery {
  status?: ReservationStatus;
  date?: string;
  roomId?: string;
  customerId?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 预订API服务
 * 处理所有与预订相关的API请求
 */
export class ReservationApiService extends BaseApiService {
  constructor() {
    super('/api/reservations');
  }

  /**
   * 创建新预订
   */
  async createReservation(reservation: Omit<Reservation, 'id' | 'createdAt' | 'updatedAt'>): Promise<Reservation> {
    return this.post<ApiResponse<Reservation>>('', reservation).then(response => response.data);
  }

  /**
   * 获取预订详情
   */
  async getReservationById(reservationId: string): Promise<Reservation> {
    return this.get<ApiResponse<Reservation>>(`/${reservationId}`).then(response => response.data);
  }

  /**
   * 更新预订信息
   */
  async updateReservation(reservationId: string, reservation: Partial<Reservation>): Promise<Reservation> {
    return this.put<ApiResponse<Reservation>>(`/${reservationId}`, reservation).then(response => response.data);
  }

  /**
   * 取消预订
   */
  async cancelReservation(reservationId: string, reason?: string): Promise<Reservation> {
    return this.post<ApiResponse<Reservation>>(`/${reservationId}/cancel`, { reason }).then(response => response.data);
  }

  /**
   * 预订办理入住
   */
  async checkInReservation(reservationId: string): Promise<Reservation> {
    return this.post<ApiResponse<Reservation>>(`/${reservationId}/check-in`, {}).then(response => response.data);
  }

  /**
   * 获取当天所有预订
   */
  async getTodayReservations(): Promise<Reservation[]> {
    return this.get<ApiResponse<Reservation[]>>('/today').then(response => response.data);
  }

  /**
   * 获取特定日期的预订
   */
  async getReservationsByDate(date: string): Promise<Reservation[]> {
    return this.get<ApiResponse<Reservation[]>>(`/date/${date}`).then(response => response.data);
  }

  /**
   * 获取指定房间的预订
   */
  async getReservationsByRoom(roomId: string): Promise<Reservation[]> {
    return this.get<ApiResponse<Reservation[]>>('/room', { params: { roomId } }).then(response => response.data);
  }

  /**
   * 获取指定客户的预订
   */
  async getReservationsByCustomer(customerId: string): Promise<Reservation[]> {
    return this.get<ApiResponse<Reservation[]>>('/customer', { params: { customerId } }).then(response => response.data);
  }

  /**
   * 查询预订
   */
  async searchReservations(query: ReservationQuery): Promise<Reservation[]> {
    return this.get<ApiResponse<Reservation[]>>('/search', { params: query }).then(response => response.data);
  }

  /**
   * 检查房间在特定时间段是否可预订
   */
  async checkRoomAvailability(roomId: string, startTime: string, endTime: string): Promise<{ 
    available: boolean; 
    conflictReason?: string;
  }> {
    return this.get<ApiResponse<{ available: boolean; conflictReason?: string; }>>(
      '/availability', 
      { params: { roomId, startTime, endTime } }
    ).then(response => response.data);
  }
}
