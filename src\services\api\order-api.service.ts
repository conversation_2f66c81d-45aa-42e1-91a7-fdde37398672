import { BaseApiService, ApiResponse } from './base-api';
import type { Order, OrderForm, OrderStatus } from '@/views/cashier/types/product';

/**
 * 订单API服务
 * 处理所有与订单相关的API请求
 */
export class OrderApiService extends BaseApiService {
  constructor() {
    super('/api/orders');
  }

  /**
   * 创建订单
   */
  async createOrder(orderForm: OrderForm): Promise<Order> {
    return this.post<ApiResponse<Order>>('', orderForm).then(response => response.data);
  }

  /**
   * 获取订单详情
   */
  async getOrderById(orderId: string): Promise<Order> {
    return this.get<ApiResponse<Order>>(`/${orderId}`).then(response => response.data);
  }

  /**
   * 获取房间的所有订单
   */
  async getOrdersByRoom(roomId: string): Promise<Order[]> {
    return this.get<ApiResponse<Order[]>>('/room', { params: { roomId } }).then(response => response.data);
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<Order> {
    return this.patch<ApiResponse<Order>>(`/${orderId}/status`, { status }).then(response => response.data);
  }

  /**
   * 取消订单
   */
  async cancelOrder(orderId: string, reason?: string): Promise<Order> {
    return this.post<ApiResponse<Order>>(`/${orderId}/cancel`, { reason }).then(response => response.data);
  }

  /**
   * 获取待处理订单
   */
  async getPendingOrders(): Promise<Order[]> {
    return this.get<ApiResponse<Order[]>>('/status/pending').then(response => response.data);
  }

  /**
   * 获取指定状态的订单
   */
  async getOrdersByStatus(status: OrderStatus): Promise<Order[]> {
    return this.get<ApiResponse<Order[]>>(`/status/${status}`).then(response => response.data);
  }

  /**
   * 更新订单备注
   */
  async updateOrderNotes(orderId: string, notes: string): Promise<Order> {
    return this.patch<ApiResponse<Order>>(`/${orderId}/notes`, { notes }).then(response => response.data);
  }

  /**
   * 完成房间所有订单结账
   */
  async checkoutRoomOrders(roomId: string, checkoutData: { 
    totalAmount: number; 
    notes?: string;
    paymentMethod?: string;
  }): Promise<{ success: boolean; message: string; }> {
    return this.post<ApiResponse<{ success: boolean; message: string; }>>(
      `/room/${roomId}/checkout`, 
      checkoutData
    ).then(response => response.data);
  }
}
