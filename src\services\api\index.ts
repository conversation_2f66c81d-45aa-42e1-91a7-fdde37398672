import { RoomApiService } from './room-api.service';
import { OrderApiService } from './order-api.service';
import { ProductApiService } from './product-api.service';
import { CustomerApiService } from './customer-api.service';
import { ReservationApiService } from './reservation-api.service';

/**
 * API服务工厂类
 * 统一管理所有API服务实例
 */
export class ApiServiceFactory {
  private static roomApiInstance: RoomApiService;
  private static orderApiInstance: OrderApiService;
  private static productApiInstance: ProductApiService;
  private static customerApiInstance: CustomerApiService;
  private static reservationApiInstance: ReservationApiService;

  /**
   * 获取房间API服务实例
   */
  static getRoomApi(): RoomApiService {
    if (!this.roomApiInstance) {
      this.roomApiInstance = new RoomApiService();
    }
    return this.roomApiInstance;
  }

  /**
   * 获取订单API服务实例
   */
  static getOrderApi(): OrderApiService {
    if (!this.orderApiInstance) {
      this.orderApiInstance = new OrderApiService();
    }
    return this.orderApiInstance;
  }

  /**
   * 获取商品API服务实例
   */
  static getProductApi(): ProductApiService {
    if (!this.productApiInstance) {
      this.productApiInstance = new ProductApiService();
    }
    return this.productApiInstance;
  }

  /**
   * 获取客户API服务实例
   */
  static getCustomerApi(): CustomerApiService {
    if (!this.customerApiInstance) {
      this.customerApiInstance = new CustomerApiService();
    }
    return this.customerApiInstance;
  }

  /**
   * 获取预订API服务实例
   */
  static getReservationApi(): ReservationApiService {
    if (!this.reservationApiInstance) {
      this.reservationApiInstance = new ReservationApiService();
    }
    return this.reservationApiInstance;
  }
}

// 导出具体的API服务类型，方便类型引用
export type { RoomApiService } from './room-api.service';
export type { OrderApiService } from './order-api.service';
export type { ProductApiService } from './product-api.service';
export type { CustomerApiService, Customer } from './customer-api.service';
export type { ReservationApiService, Reservation, ReservationStatus } from './reservation-api.service';
export type { ApiResponse } from './base-api';

// 导出便捷的API访问方法
export const roomApi = ApiServiceFactory.getRoomApi();
export const orderApi = ApiServiceFactory.getOrderApi();
export const productApi = ApiServiceFactory.getProductApi();
export const customerApi = ApiServiceFactory.getCustomerApi();
export const reservationApi = ApiServiceFactory.getReservationApi();
