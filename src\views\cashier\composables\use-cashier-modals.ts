import { ref } from "vue";
import type { Room } from "../types/room";
import type { OrderForm } from "../types/product";
import { useOrderStore } from "@/store/cashier/order";
import { ElMessage } from "element-plus";

export function useCashierModals() {
  const orderStore = useOrderStore();

  // 商品选择弹窗状态
  const showProductModal = ref(false);
  const productModalRoomInfo = ref<{ id: string; type: string }>({
    id: "",
    type: ""
  });

  // 结账确认弹窗状态
  const showCheckoutModal = ref(false);
  const checkoutRoomInfo = ref<Room | null>(null);

  // 打开商品选择弹窗
  const openProductModal = (room: Room) => {
    productModalRoomInfo.value = { id: room.id, type: room.type };
    showProductModal.value = true;
  };

  // 关闭商品选择弹窗
  const closeProductModal = () => {
    showProductModal.value = false;
  };

  // 确认订单
  const handleOrderConfirm = (orderForm: OrderForm) => {
    orderStore.addOrder(orderForm);
    closeProductModal();
    ElMessage.success("落单成功");
  };

  // 打开结账确认弹窗
  const openCheckoutModal = (room: Room) => {
    checkoutRoomInfo.value = room;
    showCheckoutModal.value = true;
  };

  // 关闭结账确认弹窗
  const closeCheckoutModal = () => {
    showCheckoutModal.value = false;
  };

  return {
    // state
    showProductModal,
    productModalRoomInfo,
    showCheckoutModal,
    checkoutRoomInfo,

    // methods
    openProductModal,
    closeProductModal,
    handleOrderConfirm,
    openCheckoutModal,
    closeCheckoutModal
  };
}
