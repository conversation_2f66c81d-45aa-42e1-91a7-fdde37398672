<template>
  <div
    class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center"
  >
    <div
      class="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl w-full max-w-7xl h-[90vh] flex flex-col border border-white/20"
    >
      <!-- 头部 -->
      <div
        class="flex items-center justify-between p-6 border-b border-slate-200/50"
      >
        <div class="flex items-center space-x-4">
          <div
            class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center"
          >
            <font-awesome-icon
              icon="shopping-cart"
              class="text-white text-lg"
            />
          </div>
          <div>
            <h2 class="text-2xl font-bold text-slate-800">商品落单</h2>
            <p class="text-sm text-slate-500">
              {{ roomInfo.id }} - {{ roomInfo.type }}
            </p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="w-10 h-10 flex items-center justify-center text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-xl transition-all duration-200"
        >
          <font-awesome-icon icon="times" class="text-lg" />
        </button>
      </div>

      <div class="flex flex-1 overflow-hidden">
        <!-- 左侧：商品搜索和列表 -->
        <div class="flex-1 flex flex-col border-r border-slate-200/50">
          <!-- 搜索栏 -->
          <div class="p-6 border-b border-slate-100/50">
            <div class="relative">
              <input
                v-model="searchKeyword"
                type="text"
                placeholder="搜索商品名称..."
                class="w-full pl-12 pr-4 py-3 bg-white/80 backdrop-blur-sm border border-slate-300/50 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200"
              />
              <font-awesome-icon
                icon="search"
                class="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400"
              />
            </div>

            <!-- 分类筛选 -->
            <div class="flex flex-wrap gap-2 mt-4">
              <button
                v-for="(config, category) in PRODUCT_CATEGORIES"
                :key="category"
                @click="
                  selectedCategory =
                    selectedCategory === category ? '' : category
                "
                :class="[
                  'px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200',
                  selectedCategory === category
                    ? 'bg-blue-500 text-white shadow-lg'
                    : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
                ]"
              >
                <font-awesome-icon :icon="config.icon" class="mr-1" />
                {{ config.label }}
              </button>
            </div>
          </div>

          <!-- 商品列表 -->
          <div class="flex-1 overflow-auto p-6">
            <div class="grid grid-cols-2 lg:grid-cols-3 gap-4">
              <div
                v-for="product in filteredProducts"
                :key="product.id"
                class="bg-white/80 backdrop-blur-sm border border-slate-200/50 rounded-xl p-4 hover:shadow-lg hover:scale-105 transition-all duration-200 cursor-pointer"
                @click="addToCart(product)"
              >
                <div class="flex items-center justify-between mb-3">
                  <h3 class="font-semibold text-slate-800 text-sm">
                    {{ product.name }}
                  </h3>
                  <span
                    class="px-2 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-600"
                  >
                    {{ PRODUCT_CATEGORIES[product.category].label }}
                  </span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-lg font-bold text-blue-600"
                    >¥{{ product.price }}</span
                  >
                  <span class="text-xs text-slate-500">{{ product.unit }}</span>
                </div>
                <div class="flex items-center justify-between mt-2">
                  <span class="text-xs text-slate-500"
                    >库存: {{ product.stock }}</span
                  >
                  <button
                    class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
                  >
                    <font-awesome-icon icon="plus" class="text-xs" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：购物车 -->
        <div class="w-96 flex flex-col bg-slate-50/50 backdrop-blur-sm">
          <!-- 购物车头部 -->
          <div class="p-6 border-b border-slate-200/50">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-slate-800">购物车</h3>
              <span
                class="bg-blue-500 text-white px-2 py-1 rounded-full text-sm"
              >
                {{ cartItems.length }}
              </span>
            </div>
          </div>

          <!-- 购物车列表 -->
          <div class="flex-1 overflow-auto p-6">
            <div v-if="cartItems.length === 0" class="text-center py-12">
              <font-awesome-icon
                icon="shopping-cart"
                class="text-4xl text-slate-300 mb-4"
              />
              <p class="text-slate-500">购物车为空</p>
              <p class="text-sm text-slate-400">点击左侧商品添加到购物车</p>
            </div>

            <div v-else class="space-y-3">
              <div
                v-for="item in cartItems"
                :key="item.productId"
                class="bg-white/80 backdrop-blur-sm border border-slate-200/50 rounded-lg p-3"
              >
                <div class="flex items-start justify-between mb-2">
                  <h4 class="font-medium text-slate-800 text-sm">
                    {{ item.product.name }}
                  </h4>
                  <button
                    @click="removeFromCart(item.productId)"
                    class="text-red-500 hover:text-red-700 transition-colors"
                  >
                    <font-awesome-icon icon="trash-alt" class="text-xs" />
                  </button>
                </div>

                <div class="flex items-center justify-between">
                  <span class="text-sm text-slate-600"
                    >¥{{ item.product.price }} / {{ item.product.unit }}</span
                  >
                  <div class="flex items-center space-x-2">
                    <button
                      @click="updateQuantity(item.productId, item.quantity - 1)"
                      :disabled="item.quantity <= 1"
                      class="w-6 h-6 bg-slate-200 text-slate-600 rounded-full flex items-center justify-center hover:bg-slate-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <font-awesome-icon icon="minus" class="text-xs" />
                    </button>
                    <span class="w-8 text-center text-sm font-medium">{{
                      item.quantity
                    }}</span>
                    <button
                      @click="updateQuantity(item.productId, item.quantity + 1)"
                      class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
                    >
                      <font-awesome-icon icon="plus" class="text-xs" />
                    </button>
                  </div>
                </div>

                <div
                  class="flex items-center justify-between mt-2 pt-2 border-t border-slate-100"
                >
                  <span class="text-sm text-slate-500">小计</span>
                  <span class="font-semibold text-slate-800"
                    >¥{{ item.subtotal }}</span
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- 购物车底部：总计和操作 -->
          <div
            class="p-6 border-t border-slate-200/50 bg-white/80 backdrop-blur-sm"
          >
            <div class="space-y-3 mb-4">
              <div class="flex items-center justify-between">
                <span class="text-slate-600">商品数量</span>
                <span class="font-medium">{{ totalQuantity }} 件</span>
              </div>
              <div class="flex items-center justify-between text-lg">
                <span class="font-semibold text-slate-800">总计</span>
                <span class="font-bold text-blue-600">¥{{ totalAmount }}</span>
              </div>
            </div>

            <textarea
              v-model="orderNotes"
              placeholder="订单备注（可选）"
              rows="2"
              class="w-full p-3 bg-white/80 backdrop-blur-sm border border-slate-300/50 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-sm mb-4"
            ></textarea>

            <div class="flex space-x-3">
              <button
                @click="clearCart"
                :disabled="cartItems.length === 0"
                class="flex-1 py-3 bg-slate-200 text-slate-600 rounded-lg hover:bg-slate-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                清空
              </button>
              <button
                @click="confirmOrder"
                :disabled="cartItems.length === 0"
                class="flex-1 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
              >
                确认落单
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type {
  Product,
  CartItem,
  ProductCategory,
  OrderForm
} from "../../types/product";
import { PRODUCT_CATEGORIES } from "../../types/product";

defineOptions({
  name: "ProductSelectionModal"
});

// Props
const props = defineProps<{
  roomInfo: {
    id: string;
    type: string;
  };
}>();

// Emits
const emit = defineEmits<{
  close: [];
  confirm: [order: OrderForm];
}>();

// 响应式数据
const searchKeyword = ref("");
const selectedCategory = ref<ProductCategory | "">("");
const cartItems = ref<CartItem[]>([]);
const orderNotes = ref("");
const products = ref<Product[]>([]);

// 模拟商品数据
const mockProducts: Product[] = [
  {
    id: "1",
    name: "可口可乐",
    price: 8,
    category: "beverage",
    description: "经典可乐",
    stock: 50,
    unit: "瓶",
    isActive: true,
    createdAt: "2025-01-01",
    updatedAt: "2025-01-01"
  },
  {
    id: "2",
    name: "薯片",
    price: 15,
    category: "snack",
    description: "香脆薯片",
    stock: 30,
    unit: "包",
    isActive: true,
    createdAt: "2025-01-01",
    updatedAt: "2025-01-01"
  },
  {
    id: "3",
    name: "苹果",
    price: 25,
    category: "fruit",
    description: "新鲜苹果",
    stock: 20,
    unit: "份",
    isActive: true,
    createdAt: "2025-01-01",
    updatedAt: "2025-01-01"
  },
  {
    id: "4",
    name: "青岛啤酒",
    price: 12,
    category: "alcohol",
    description: "经典啤酒",
    stock: 40,
    unit: "瓶",
    isActive: true,
    createdAt: "2025-01-01",
    updatedAt: "2025-01-01"
  },
  {
    id: "5",
    name: "中华香烟",
    price: 25,
    category: "cigarette",
    description: "软包中华",
    stock: 15,
    unit: "包",
    isActive: true,
    createdAt: "2025-01-01",
    updatedAt: "2025-01-01"
  }
];

// 计算属性
const filteredProducts = computed(() => {
  let filtered = products.value;

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(p => p.name.toLowerCase().includes(keyword));
  }

  if (selectedCategory.value) {
    filtered = filtered.filter(p => p.category === selectedCategory.value);
  }

  return filtered.filter(p => p.isActive && p.stock > 0);
});

const totalQuantity = computed(() => {
  return cartItems.value.reduce((sum, item) => sum + item.quantity, 0);
});

const totalAmount = computed(() => {
  return cartItems.value.reduce((sum, item) => sum + item.subtotal, 0);
});

// 方法
const addToCart = (product: Product) => {
  const existingItem = cartItems.value.find(
    item => item.productId === product.id
  );

  if (existingItem) {
    updateQuantity(product.id, existingItem.quantity + 1);
  } else {
    const newItem: CartItem = {
      productId: product.id,
      product,
      quantity: 1,
      subtotal: product.price
    };
    cartItems.value.push(newItem);
  }

  ElMessage.success(`已添加 ${product.name} 到购物车`);
};

const removeFromCart = (productId: string) => {
  const index = cartItems.value.findIndex(item => item.productId === productId);
  if (index > -1) {
    cartItems.value.splice(index, 1);
  }
};

const updateQuantity = (productId: string, newQuantity: number) => {
  if (newQuantity <= 0) {
    removeFromCart(productId);
    return;
  }

  const item = cartItems.value.find(item => item.productId === productId);
  if (item) {
    item.quantity = newQuantity;
    item.subtotal = item.product.price * newQuantity;
  }
};

const clearCart = () => {
  cartItems.value = [];
  ElMessage.info("购物车已清空");
};

const confirmOrder = () => {
  if (cartItems.value.length === 0) {
    ElMessage.warning("请先添加商品到购物车");
    return;
  }

  const orderForm: OrderForm = {
    roomId: props.roomInfo.id,
    items: cartItems.value,
    notes: orderNotes.value || undefined
  };

  emit("confirm", orderForm);
  ElMessage.success("订单已确认");
};

// 生命周期
onMounted(() => {
  products.value = mockProducts;
});
</script>
