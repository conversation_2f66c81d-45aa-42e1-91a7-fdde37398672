{"name": "pure-admin-thin", "version": "5.9.0", "private": true, "type": "module", "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f . -r", "clean:cache": "rimraf .eslintcache && rimraf pnpm-lock.yaml && rimraf node_modules && pnpm store prune && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "prepare": "husky", "preinstall": "npx only-allow pnpm"}, "keywords": ["pure-admin-thin", "vue-pure-admin", "element-plus", "tailwindcss", "pure-admin", "typescript", "pinia", "vue3", "vite", "esm"], "homepage": "https://github.com/pure-admin/pure-admin-thin", "repository": {"type": "git", "url": "git+https://github.com/pure-admin/pure-admin-thin.git"}, "bugs": {"url": "https://github.com/pure-admin/vue-pure-admin/issues"}, "license": "MIT", "author": {"name": "xiaoxian521", "email": "<EMAIL>", "url": "https://github.com/xiaoxian521"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^3.2.1", "@pureadmin/utils": "^2.5.0", "@vueuse/core": "^12.0.0", "@vueuse/motion": "^2.2.6", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "axios": "^1.10.0", "axios-mock-adapter": "^2.1.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "^2.9.0", "file-saver": "^2.0.5", "gridstack": "^12.1.1", "js-cookie": "^3.0.5", "localforage": "^1.10.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "pinia": "^2.3.0", "pinyin-pro": "^3.26.0", "pmpn": "^1.0.0", "qs": "^6.13.1", "responsive-storage": "^2.2.0", "sortablejs": "^1.15.6", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-tippy": "^6.5.0", "vue-types": "^5.1.3", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@commitlint/types": "^19.5.0", "@eslint/js": "^9.16.0", "@faker-js/faker": "^9.3.0", "@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.2.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20.17.9", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.17", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "autoprefixer": "^10.4.20", "boxen": "^8.0.1", "code-inspector-plugin": "^0.18.2", "cssnano": "^7.0.6", "eslint": "^9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "gradient-string": "^3.0.0", "husky": "^9.1.7", "lint-staged": "^15.2.10", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-import": "^16.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.4.2", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.82.0", "stylelint": "^16.11.0", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.2", "svgo": "^3.3.2", "tailwindcss": "^3.4.16", "typescript": "5.6.3", "vite": "^6.0.3", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-fake-server": "^2.1.4", "vite-plugin-remove-console": "^2.2.0", "vite-plugin-router-warn": "^1.0.0", "vite-svg-loader": "^5.1.0", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.1.10"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=22.0.0", "pnpm": ">=9"}, "pnpm": {"allowedDeprecatedVersions": {"are-we-there-yet": "*", "sourcemap-codec": "*", "domexception": "*", "w3c-hr-time": "*", "inflight": "*", "npmlog": "*", "rimraf": "*", "stable": "*", "gauge": "*", "abab": "*", "glob": "*"}, "peerDependencyRules": {"allowedVersions": {"eslint": "9"}}}}