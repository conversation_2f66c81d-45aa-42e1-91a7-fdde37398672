<template>
  <div class="space-y-6">
    <!-- 区域显示 -->
    <div
      v-for="area in filteredAreas"
      :key="area.key"
      class="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100"
    >
      <!-- 区域头部 -->
      <div :class="['p-4 border-b border-slate-100']">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div :class="['w-3 h-3 rounded-full', area.dotColor]"></div>
            <h3 :class="['text-lg font-bold', area.textColor]">
              <font-awesome-icon :icon="area.icon" class="mr-2" />
              {{ area.label }}
            </h3>
            <span
              :class="[
                'inline-flex items-center px-3 py-1 rounded-full text-sm font-bold',
                getAreaTagClass(area.tagType)
              ]"
            >
              {{ area.rooms.length }} 间
            </span>
          </div>

          <!-- 折叠按钮 -->
          <button
            @click="toggleAreaCollapse(area.key)"
            class="flex items-center justify-center w-8 h-8 text-slate-500 hover:text-blue-600 hover:bg-white/60 rounded-lg transition-all duration-200"
          >
            <font-awesome-icon
              :icon="isAreaCollapsed(area.key) ? 'chevron-down' : 'chevron-up'"
              class="text-sm"
            />
          </button>
        </div>
      </div>

      <!-- 房间网格 -->
      <div v-if="!isAreaCollapsed(area.key)" class="p-4">
        <div
          v-if="area.rooms.length > 0"
          class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3"
        >
          <room-card
            v-for="room in area.rooms"
            :key="room.id"
            :room="room"
            :selected-room="selectedRoom"
            @select-room="selectRoom"
            @room-action="handleRoomAction"
          />
        </div>
        <div v-else class="text-center py-8 text-gray-500">
          <font-awesome-icon icon="inbox" class="text-4xl mb-2" />
          <p>该区域暂无房间</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import type { Room, AreaConfig } from "../../types/room";
import RoomCard from "./room-card.vue";

defineOptions({
  name: "RoomGrid"
});

// Props
interface Props {
  filteredAreas: (AreaConfig & { rooms: Room[] })[];
  selectedRoom: Room | null;
}

const props = defineProps<Props>();

// Emits
// 监听筛选区域变化，当只剩一个区域时自动展开
watch(
  () => props.filteredAreas,
  newAreas => {
    if (newAreas.length === 1) {
      collapsedAreas[newAreas[0].key] = false;
    }
  },
  { immediate: true }
);

const emit = defineEmits<{
  selectRoom: [room: Room];
  roomAction: [action: string, room: Room];
}>();

// 折叠状态管理
const collapsedAreas = reactive<Record<string, boolean>>({});
const allCollapsed = ref(false);

// 切换区域折叠状态
const toggleAreaCollapse = (areaKey: string) => {
  collapsedAreas[areaKey] = !collapsedAreas[areaKey];
};

// 检查区域是否折叠
const isAreaCollapsed = (areaKey: string): boolean => {
  return collapsedAreas[areaKey] || false;
};

// 全部折叠/展开
const toggleAllCollapse = () => {
  allCollapsed.value = !allCollapsed.value;

  // 更新所有区域的折叠状态
  props.filteredAreas.forEach(area => {
    collapsedAreas[area.key] = allCollapsed.value;
  });
};

// 获取区域标签样式
const getAreaTagClass = (tagType?: string): string => {
  const tagClasses: Record<string, string> = {
    primary: "bg-blue-100 text-blue-800",
    success: "bg-green-100 text-green-800",
    warning: "bg-orange-100 text-orange-800",
    danger: "bg-red-100 text-red-800",
    info: "bg-gray-100 text-gray-800"
  };
  return tagClasses[tagType || "info"] || tagClasses.info;
};

// 选择房间
const selectRoom = (room: Room) => {
  emit("selectRoom", room);
};

// 处理房间操作
const handleRoomAction = (action: string, room: Room) => {
  emit("roomAction", action, room);
};

// 暴露给父组件
defineExpose({
  toggleAllCollapse,
  allCollapsed
});
</script>
