import { BaseApiService, ApiResponse } from './base-api';
import type { Room, RoomStatus } from '@/views/cashier/types/room';

/**
 * 房间API服务
 * 处理所有与房间相关的API请求
 */
export class RoomApiService extends BaseApiService {
  constructor() {
    super('/api/rooms');
  }

  /**
   * 获取所有房间
   */
  async getAllRooms(): Promise<Room[]> {
    return this.get<ApiResponse<Room[]>>('').then(response => response.data);
  }

  /**
   * 获取房间详情
   */
  async getRoomById(roomId: string): Promise<Room> {
    return this.get<ApiResponse<Room>>(`/${roomId}`).then(response => response.data);
  }

  /**
   * 更新房间状态
   */
  async updateRoomStatus(roomId: string, status: RoomStatus): Promise<Room> {
    return this.patch<ApiResponse<Room>>(`/${roomId}/status`, { status }).then(response => response.data);
  }

  /**
   * 开房操作
   */
  async openRoom(roomId: string, customerData: { customer: string, notes?: string }): Promise<Room> {
    return this.post<ApiResponse<Room>>(`/${roomId}/open`, customerData).then(response => response.data);
  }

  /**
   * 结账操作
   */
  async checkoutRoom(roomId: string, checkoutData: { notes?: string }): Promise<Room> {
    return this.post<ApiResponse<Room>>(`/${roomId}/checkout`, checkoutData).then(response => response.data);
  }

  /**
   * 获取某一区域的房间
   */
  async getRoomsByArea(area: string): Promise<Room[]> {
    return this.get<ApiResponse<Room[]>>(`/area/${area}`).then(response => response.data);
  }

  /**
   * 按房间状态筛选
   */
  async getRoomsByStatus(status: RoomStatus): Promise<Room[]> {
    return this.get<ApiResponse<Room[]>>(`/status/${status}`).then(response => response.data);
  }

  /**
   * 搜索房间
   */
  async searchRooms(keyword: string): Promise<Room[]> {
    return this.get<ApiResponse<Room[]>>('/search', { params: { keyword } }).then(response => response.data);
  }
}
