import { ElMessage } from "element-plus";
import type { Room } from "../types/room";
import { roomApi } from "@/services/api";

/**
 * 房间操作Composable
 * 处理房间相关业务逻辑，通过API服务层与后端交互
 */
export function useRoomOperations() {
  // 错误处理辅助函数
  const handleApiError = (error: any, fallbackMessage: string) => {
    console.error(error);
    ElMessage.error(error.message || fallbackMessage);
  };
  
  // 处理房间操作
  const handleRoomAction = (action: string, room: Room) => {
    switch (action) {
      case "book":
        handleBooking(room);
        break;
      case "transfer":
        handleTransfer(room);
        break;
      case "add-items":
        handleAddItems(room);
        break;
      case "cancel":
        handleCancel(room);
        break;
      case "clean-done":
        handleCleanDone(room);
        break;
      case "repair-done":
        handleRepairDone(room);
        break;
      case "enable":
        handleEnable(room);
        break;
      case "place-order":
        handlePlaceOrder(room);
        break;
      default:
        ElMessage.info(`操作 ${action} 暂未实现`);
    }
  };

  // 开房操作
  const openRoom = async (roomId: string, customer: string) => {
    try {
      await roomApi.openRoom(roomId, { customer });
      ElMessage.success(`已开通房间 ${roomId} 给客户 ${customer}`);
    } catch (error) {
      handleApiError(error, `开房失败，请稍后重试`);
    }
  };

  // 结账操作
  const checkout = async (roomId: string) => {
    try {
      await roomApi.checkoutRoom(roomId, {});
      ElMessage.success(`房间 ${roomId} 结账成功`);
    } catch (error) {
      handleApiError(error, `结账失败，请稍后重试`);
    }
  };

  // 预订操作
  const handleBooking = (room: Room) => {
    ElMessage.info(`预订操作 - 房间: ${room.id}`);
    // TODO: 打开预订弹窗
  };

  // 换房操作
  const handleTransfer = (room: Room) => {
    ElMessage.info(`换房操作 - 房间: ${room.id}`);
    // TODO: 打开换房弹窗
  };

  // 加单操作
  const handleAddItems = (room: Room) => {
    ElMessage.info(`加单操作 - 房间: ${room.id}`);
    // TODO: 打开加单弹窗
  };

  // 落单操作
  const handlePlaceOrder = (_room: Room) => {
    // 实际的弹窗逻辑已在 index.vue 中处理
  };

  // 取消预订
  const handleCancel = async (room: Room) => {
    try {
      await roomApi.updateRoomStatus(room.id, 'free');
      ElMessage.success(`房间 ${room.id} 预订已取消`);
    } catch (error) {
      handleApiError(error, `取消预订失败`);
    }
  };

  // 清洁完成
  const handleCleanDone = async (room: Room) => {
    try {
      await roomApi.updateRoomStatus(room.id, 'free');
      ElMessage.success(`房间 ${room.id} 清洁完成，现已可用`);
    } catch (error) {
      handleApiError(error, `更新房间状态失败`);
    }
  };

  // 维修完成
  const handleRepairDone = async (room: Room) => {
    try {
      await roomApi.updateRoomStatus(room.id, 'free');
      ElMessage.success(`房间 ${room.id} 维修完成，现已可用`);
    } catch (error) {
      handleApiError(error, `更新房间状态失败`);
    }
  };

  // 启用房间
  const handleEnable = async (room: Room) => {
    try {
      await roomApi.updateRoomStatus(room.id, 'free');
      ElMessage.success(`房间 ${room.id} 已启用并可使用`);
    } catch (error) {
      handleApiError(error, `启用房间失败`);
    }
  };

  return {
    openRoom,
    checkout,
    handleRoomAction,
    handleBooking,
    handleTransfer,
    handleAddItems,
    handlePlaceOrder,
    handleCancel,
    handleCleanDone,
    handleRepairDone,
    handleEnable
  };
}
