<template>
  <div
    class="cashier-system min-h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50"
  >
    <!-- 顶部导航栏 -->
    <cashier-header
      v-model:search-keyword="searchKeyword"
      @search="handleSearch"
    />

    <!-- 主内容区域 -->
    <main class="flex-grow container mx-auto p-4 sm:p-6">
      <div class="grid grid-cols-12 gap-6">
        <!-- 左侧信息栏 -->
        <div class="col-span-12 lg:col-span-2">
          <info-sidebar
            :room-statuses="roomStatuses"
            :quick-actions="quickActions"
            :recent-openings="recentOpenings"
            :current-status-filter="currentStatusFilter"
            @filter-by-status="handleStatusFilter"
            @quick-action="handleQuickAction"
          />
        </div>

        <!-- 中间核心区 -->
        <div class="col-span-12 lg:col-span-7">
          <!-- 区域筛选 -->
          <area-filter
            :areas="areas"
            :current-area="currentArea"
            :all-collapsed="roomGridRef?.allCollapsed"
            @switch-area="switchArea"
            @toggle-collapse="roomGridRef?.toggleAllCollapse()"
          />

          <!-- 房间状态网格 -->
          <room-grid
            ref="roomGridRef"
            :filtered-areas="filteredAreasWithRooms"
            :selected-room="selectedRoom"
            @select-room="selectRoom"
            @room-action="handleRoomAction"
          />
        </div>

        <!-- 右侧详情面板 -->
        <aside class="col-span-12 lg:col-span-3">
          <room-detail-panel
            :selected-room="selectedRoom"
            :orders="getRoomOrders(selectedRoom?.id || '')"
            :total-amount="getRoomTotalAmount(selectedRoom?.id || '')"
            @room-action="handleRoomAction"
          />
        </aside>
      </div>
    </main>

    <!-- 商品选择弹窗 -->
    <product-selection-modal
      v-if="showProductModal"
      :room-info="productModalRoomInfo"
      @close="closeProductModal"
      @confirm="handleOrderConfirm"
    />

    <!-- 结账确认弹窗 -->
    <checkout-confirmation-modal
      v-if="showCheckoutModal"
      :room-info="checkoutRoomInfo"
      :room-orders="getRoomOrders(checkoutRoomInfo?.id || '')"
      @close="closeCheckoutModal"
      @confirm="handleCheckoutConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";

// 导入组件
import CashierHeader from "./components/layout/cashier-header.vue";
import InfoSidebar from "./components/layout/info-sidebar.vue";
import AreaFilter from "./components/room/area-filter.vue";
import RoomGrid from "./components/room/room-grid.vue";
import RoomDetailPanel from "./components/details/room-detail-panel.vue";
import ProductSelectionModal from "./components/dialogs/product-selection-modal.vue";
import CheckoutConfirmationModal from "./components/dialogs/checkout-confirmation-modal.vue";

// 导入 composables
import { useRoomData } from "./composables/use-room-data";
import { useRoomOperations } from "./composables/use-room-operations";
import { useSearchFilter } from "./composables/use-search-filter";
import { useCashierModals } from "./composables/use-cashier-modals";

// 导入状态管理
import { useOrderStore } from "@/store/cashier/order";

// 导入常量和工具函数
import {
  QUICK_ACTIONS,
  RECENT_OPENINGS,
  AREA_DISPLAY_CONFIG
} from "./constants/room-config";
import type { Room } from "./types/room";
import type { OrderForm } from "./types/product";

defineOptions({
  name: "CashierIndex"
});

// 使用 composables
const { allRooms, roomStatuses, selectedRoom, selectRoom } = useRoomData();
const roomGridRef = ref<InstanceType<typeof RoomGrid> | null>(null);

const roomOperations = useRoomOperations();

// 使用订单状态管理
const orderStore = useOrderStore();

const {
  searchKeyword,
  currentArea,
  areas,
  handleSearch,
  switchArea,
  filterByStatus
} = useSearchFilter();

// 弹窗管理
const {
  showProductModal,
  productModalRoomInfo,
  showCheckoutModal,
  checkoutRoomInfo,
  openProductModal,
  closeProductModal,
  handleOrderConfirm,
  openCheckoutModal,
  closeCheckoutModal
} = useCashierModals();

// 当前状态筛选
const currentStatusFilter = ref("");

// 常量数据
const quickActions = QUICK_ACTIONS;
const recentOpenings = RECENT_OPENINGS;

// 计算属性 - 带房间数据的区域配置
const filteredAreasWithRooms = computed(() => {
  return Object.values(AREA_DISPLAY_CONFIG)
    .map(area => ({
      ...area,
      rooms: allRooms.filter(room => {
        // 首先按区域筛选 - 每个区域只显示属于该区域的房间
        const matchesArea = room.area === area.key;

        // 然后按搜索关键词筛选
        const matchesSearch =
          searchKeyword.value === "" ||
          room.id.toLowerCase().includes(searchKeyword.value.toLowerCase());

        // 最后按状态筛选（如果有激活的状态筛选）
        const matchesStatus =
          currentStatusFilter.value === "" ||
          room.status === currentStatusFilter.value;

        return matchesArea && matchesSearch && matchesStatus;
      })
    }))
    .filter(area => {
      // 只有在选择了特定区域时才过滤区域，否则显示所有区域
      return currentArea.value === "all" || area.key === currentArea.value;
    });
});

// 处理状态筛选
const handleStatusFilter = (status: string) => {
  if (currentStatusFilter.value === status) {
    // 如果点击的是当前激活的状态，则取消筛选
    currentStatusFilter.value = "";
  } else {
    // 否则设置新的状态筛选
    currentStatusFilter.value = status;
  }
};

// 处理快捷操作
const handleQuickAction = (action: string) => {
  switch (action) {
    case "booking":
      ElMessage.info("预订功能开发中...");
      break;
    case "member":
      ElMessage.info("会员功能开发中...");
      break;
    case "report":
      ElMessage.info("报表功能开发中...");
      break;
  }
};

// 处理房间操作
const handleRoomAction = (action: string, room: Room) => {
  switch (action) {
    case "open-room":
      roomOperations.openRoom(room.id, "散客");
      break;
    case "add-order":
      openProductModal(room);
      break;
    case "checkout":
      openCheckoutModal(room);
      break;
    default:
      ElMessage.info(`操作 ${action} 暂未实现`);
  }
};

// 确认结账
const handleCheckoutConfirm = (checkoutData: {
  roomId: string;
  totalAmount: number;
  notes?: string;
  orders: any[];
}) => {
  roomOperations.checkout(checkoutData.roomId);
  orderStore.clearRoomOrders(checkoutData.roomId);
  closeCheckoutModal();

  ElMessage({
    message: `房间 ${checkoutData.roomId} 结账成功！消费总额：¥${checkoutData.totalAmount}`,
    type: "success",
    duration: 5000,
    showClose: true
  });
};

// 获取房间订单
const getRoomOrders = (roomId: string) => {
  return orderStore.getOrdersByRoom(roomId);
};

// 获取房间总消费金额
const getRoomTotalAmount = (roomId: string) => {
  return orderStore.getRoomTotalAmount(roomId);
};

// 初始化
onMounted(() => {
  orderStore.loadFromLocalStorage();
  orderStore.initializeProducts();
});
</script>

<style scoped>
/* 收银系统专用样式 */
.cashier-system {
  font-family: "Inter", "Noto Sans SC", sans-serif;
}

/* 房间卡片样式 */
.room-card {
  transition: all 0.2s ease-in-out;
}

.room-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* 玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义按钮样式 */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-warning {
  @apply bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

/* 自定义输入框样式 */
.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
}

/* 自定义标签样式 */
.tag {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.tag-primary {
  @apply bg-blue-100 text-blue-800;
}

.tag-success {
  @apply bg-green-100 text-green-800;
}

.tag-warning {
  @apply bg-orange-100 text-orange-800;
}

.tag-danger {
  @apply bg-red-100 text-red-800;
}

.tag-info {
  @apply bg-gray-100 text-gray-800;
}
</style>
