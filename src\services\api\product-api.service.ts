import { BaseApiService, ApiResponse } from './base-api';
import type { Product, ProductQuery, ProductCategory } from '@/views/cashier/types/product';

/**
 * 产品API服务
 * 处理所有与产品和商品相关的API请求
 */
export class ProductApiService extends BaseApiService {
  constructor() {
    super('/api/products');
  }

  /**
   * 获取所有产品
   */
  async getAllProducts(): Promise<Product[]> {
    return this.get<ApiResponse<Product[]>>('').then(response => response.data);
  }

  /**
   * 获取产品详情
   */
  async getProductById(productId: string): Promise<Product> {
    return this.get<ApiResponse<Product>>(`/${productId}`).then(response => response.data);
  }

  /**
   * 按分类获取产品
   */
  async getProductsByCategory(category: ProductCategory): Promise<Product[]> {
    return this.get<ApiResponse<Product[]>>(`/category/${category}`).then(response => response.data);
  }

  /**
   * 搜索产品
   */
  async searchProducts(query: ProductQuery): Promise<Product[]> {
    return this.get<ApiResponse<Product[]>>('/search', { params: query }).then(response => response.data);
  }

  /**
   * 创建新产品
   */
  async createProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {
    return this.post<ApiResponse<Product>>('', product).then(response => response.data);
  }

  /**
   * 更新产品
   */
  async updateProduct(productId: string, product: Partial<Product>): Promise<Product> {
    return this.put<ApiResponse<Product>>(`/${productId}`, product).then(response => response.data);
  }

  /**
   * 更新产品库存
   */
  async updateProductStock(productId: string, stockChange: number): Promise<Product> {
    return this.patch<ApiResponse<Product>>(`/${productId}/stock`, { stockChange }).then(response => response.data);
  }

  /**
   * 获取热门产品
   */
  async getPopularProducts(limit: number = 10): Promise<Product[]> {
    return this.get<ApiResponse<Product[]>>('/popular', { params: { limit } }).then(response => response.data);
  }

  /**
   * 设置产品状态（激活/停用）
   */
  async setProductStatus(productId: string, isActive: boolean): Promise<Product> {
    return this.patch<ApiResponse<Product>>(`/${productId}/status`, { isActive }).then(response => response.data);
  }
}
