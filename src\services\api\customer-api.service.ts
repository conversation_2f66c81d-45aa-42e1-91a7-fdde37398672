import { BaseApiService, ApiResponse } from './base-api';

/**
 * 客户信息接口
 */
export interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  memberLevel?: string;
  points?: number;
  totalSpent?: number;
  visitCount?: number;
  createdAt: string;
  updatedAt: string;
  lastVisit?: string;
  notes?: string;
}

/**
 * 客户API服务
 * 处理所有与客户相关的API请求
 */
export class CustomerApiService extends BaseApiService {
  constructor() {
    super('/api/customers');
  }

  /**
   * 获取所有客户
   */
  async getAllCustomers(): Promise<Customer[]> {
    return this.get<ApiResponse<Customer[]>>('').then(response => response.data);
  }

  /**
   * 获取客户详情
   */
  async getCustomerById(customerId: string): Promise<Customer> {
    return this.get<ApiResponse<Customer>>(`/${customerId}`).then(response => response.data);
  }

  /**
   * 通过电话号码查找客户
   */
  async getCustomerByPhone(phone: string): Promise<Customer> {
    return this.get<ApiResponse<Customer>>('/search', { params: { phone } }).then(response => response.data);
  }

  /**
   * 创建新客户
   */
  async createCustomer(customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>): Promise<Customer> {
    return this.post<ApiResponse<Customer>>('', customer).then(response => response.data);
  }

  /**
   * 更新客户信息
   */
  async updateCustomer(customerId: string, customer: Partial<Customer>): Promise<Customer> {
    return this.put<ApiResponse<Customer>>(`/${customerId}`, customer).then(response => response.data);
  }

  /**
   * 获取客户消费记录
   */
  async getCustomerConsumption(customerId: string, page: number = 1, pageSize: number = 10): Promise<any[]> {
    return this.get<ApiResponse<any[]>>(`/${customerId}/consumption`, { 
      params: { page, pageSize } 
    }).then(response => response.data);
  }

  /**
   * 添加客户备注
   */
  async addCustomerNote(customerId: string, note: string): Promise<Customer> {
    return this.post<ApiResponse<Customer>>(`/${customerId}/notes`, { note }).then(response => response.data);
  }

  /**
   * 搜索客户
   */
  async searchCustomers(keyword: string): Promise<Customer[]> {
    return this.get<ApiResponse<Customer[]>>('/search', { params: { keyword } }).then(response => response.data);
  }
}
